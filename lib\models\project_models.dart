class Project {
  final String id;
  final String projectNumber;
  final String name;
  final String type;
  final DateTime startDate;
  final DateTime endDate;
  final List<TeamMember> teamMembers;

  Project({
    required this.id,
    required this.projectNumber,
    required this.name,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.teamMembers,
  });
}

class TeamMember {
  final String id;
  final String name;
  final String? imageUrl;
  final String role;

  TeamMember({
    required this.id,
    required this.name,
    this.imageUrl,
    required this.role,
  });
}

class ProjectTask {
  final String id;
  final String title;
  final String description;
  final String location;
  final DateTime date;
  final String timeSlot;
  final bool isCompleted;
  final String? remark;
  final String? imageUrl;

  ProjectTask({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.date,
    required this.timeSlot,
    this.isCompleted = false,
    this.remark,
    this.imageUrl,
  });
}

class Task {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final String location;
  final String status;

  Task({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.location,
    required this.status,
  });
}

