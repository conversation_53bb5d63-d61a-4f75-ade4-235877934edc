import 'dart:io';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class CameraService {
  static CameraController? _controller;
  static List<CameraDescription>? _cameras;
  static bool _isInitialized = false;

  // Initialize camera with back camera preference
  static Future<bool> initializeCamera() async {
    try {
      // Request camera permission
      final status = await Permission.camera.request();
      if (!status.isGranted) {
        return false;
      }

      // Get available cameras
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        return false;
      }

      // Find back camera or use first available
      CameraDescription camera = _cameras!.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => _cameras!.first,
      );

      // Initialize camera controller
      _controller = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();
      _isInitialized = true;
      return true;
    } catch (e) {
      print('Error initializing camera: $e');
      return false;
    }
  }

  // Get camera controller
  static CameraController? get controller => _controller;

  // Check if camera is initialized
  static bool get isInitialized => _isInitialized && _controller != null;

  // Take a picture and save it
  static Future<String?> takePicture({String? fileName}) async {
    if (!isInitialized) {
      final initialized = await initializeCamera();
      if (!initialized) return null;
    }

    try {
      // Generate filename if not provided
      fileName ??= 'attendance_${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      // Get app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final attendanceDir = Directory(path.join(directory.path, 'attendance_photos'));
      
      // Create directory if it doesn't exist
      if (!await attendanceDir.exists()) {
        await attendanceDir.create(recursive: true);
      }

      // Take picture
      final image = await _controller!.takePicture();
      
      // Move to app directory
      final savedPath = path.join(attendanceDir.path, fileName);
      await File(image.path).copy(savedPath);
      
      // Delete temporary file
      await File(image.path).delete();
      
      return savedPath;
    } catch (e) {
      print('Error taking picture: $e');
      return null;
    }
  }

  // Switch camera (front/back)
  static Future<bool> switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) {
      return false;
    }

    try {
      // Find the other camera
      final currentLensDirection = _controller?.description.lensDirection;
      final newCamera = _cameras!.firstWhere(
        (camera) => camera.lensDirection != currentLensDirection,
        orElse: () => _cameras!.first,
      );

      // Dispose current controller
      await _controller?.dispose();

      // Initialize with new camera
      _controller = CameraController(
        newCamera,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();
      return true;
    } catch (e) {
      print('Error switching camera: $e');
      return false;
    }
  }

  // Get current camera direction
  static CameraLensDirection? get currentCameraDirection {
    return _controller?.description.lensDirection;
  }

  // Dispose camera resources
  static Future<void> dispose() async {
    await _controller?.dispose();
    _controller = null;
    _isInitialized = false;
  }

  // Check if back camera is available
  static bool get hasBackCamera {
    return _cameras?.any((camera) => 
      camera.lensDirection == CameraLensDirection.back) ?? false;
  }

  // Check if front camera is available
  static bool get hasFrontCamera {
    return _cameras?.any((camera) => 
      camera.lensDirection == CameraLensDirection.front) ?? false;
  }

  // Get saved attendance photos directory
  static Future<Directory> getAttendancePhotosDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final attendanceDir = Directory(path.join(directory.path, 'attendance_photos'));
    
    if (!await attendanceDir.exists()) {
      await attendanceDir.create(recursive: true);
    }
    
    return attendanceDir;
  }

  // Delete attendance photo
  static Future<bool> deletePhoto(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting photo: $e');
      return false;
    }
  }
}
