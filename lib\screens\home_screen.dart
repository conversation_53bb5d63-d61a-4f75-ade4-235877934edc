import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lucent_employee_app/screens/face_recognition_screen.dart';
import 'package:lucent_employee_app/screens/sidebar.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState(); 
}

class _HomeScreenState extends State<HomeScreen> {
  String _location = "Fetching location...";
  String _address = "Getting address...";
  String _time = "";
  String _date = "";
  Timer? _timer;
  bool isPunchedIn = false;
  bool isOnBreak = false;
  DateTime? punchInTime;
  DateTime? breakStartTime;
  Duration totalWorkTime = Duration.zero;
  Duration totalBreakTime = Duration.zero;
  final StopWatchTimer _stopWatchTimer = StopWatchTimer();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _getLocation();
    _updateDateTime();
  }

  Future<void> _getLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      setState(() {
        _location = "Location services are disabled.";
        _address = "Unable to get address";
      });
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        setState(() {
          _location = "Location permissions are denied";
          _address = "Unable to get address";
        });
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      setState(() {
        _location = "Location permissions are permanently denied.";
        _address = "Unable to get address";
      });
      return;
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _location =
            "Lat: ${position.latitude.toStringAsFixed(4)}, Lng: ${position.longitude.toStringAsFixed(4)}";
      });
      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        setState(() {
          _address =
              "${place.name ?? ''} ${place.subLocality ?? ''}, ${place.locality ?? ''}, ${place.administrativeArea ?? ''}, ${place.country ?? ''}";
        });
      }
    } catch (e) {
      setState(() {
        _location = "Error getting location";
        _address = "Unable to get address";
      });
    }
  }

  void _updateDateTime() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final now = DateTime.now();
      setState(() {
        _time = DateFormat('HH:mm:ss').format(now);
        _date = DateFormat('dd-MM-yyyy').format(now);
      });
    });
  }

  Future<void> _openFaceRecognition() async {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            FaceRecognitionScreen(onSuccess: _completePunchIn),
      ),
    );
  }

  void _completePunchIn() {
    setState(() {
      isPunchedIn = true;
      punchInTime = DateTime.now();
      _stopWatchTimer.onStartTimer();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Successfully punched in! Work timer started.',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _punchOut() {
    _stopWatchTimer.onStopTimer();
    _stopWatchTimer.onResetTimer();

    setState(() {
      isPunchedIn = false;
      isOnBreak = false;
      punchInTime = null;
      breakStartTime = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Successfully punched out! Have a great day.',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _toggleBreak() {
    setState(() {
      if (isOnBreak) {
        // End break
        isOnBreak = false;
        breakStartTime = null;
        _stopWatchTimer.onStartTimer();
      } else {
         // Start break
        isOnBreak = true;
        breakStartTime = DateTime.now();
        _stopWatchTimer.onStopTimer();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _stopWatchTimer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = const Color(0xFF561c24);
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.grey[50],
      drawer: const Sidebar(), // <-- Use Sidebar widget here
      body: _buildBody(themeColor),
    );
  }

  Widget _buildBody(Color themeColor) {
    return Center(
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 8,
        margin: const EdgeInsets.all(24),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Live Location
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: themeColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.location_on, color: themeColor, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          "Live Location",
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: themeColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Text(
                      _address,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                     // const SizedBox(height: 8),
                      // Text(
                      //   _location,
                      //   textAlign: TextAlign.center,
                      //   style: GoogleFonts.poppins(
                      //     fontSize: 12,
                      //     color: Colors.grey[600],
                      //   ),
                      // ),
                  ],
                ),
              ),

              const SizedBox(height: 10),

              // Time & Date
              Column(
                children: [
                  Text(
                    _time,
                    style: GoogleFonts.poppins(
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                      color: themeColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _date,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Work Timer
              if (isPunchedIn) _buildTimerSection(),

              // Punch In / Out Buttons
              const SizedBox(height: 20),
              !isPunchedIn
                  ? _buildPunchInButton(themeColor)
                  : _buildBreakAndPunchOutButtons(),

              const SizedBox(height: 20),

              // Status Message
              _buildStatusMessage(themeColor),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimerSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isOnBreak ? Colors.orange[50] : Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isOnBreak ? Colors.orange[200]! : Colors.green[200]!,
        ),
      ),
      child: Column(
        children: [
          Text(
            isOnBreak ? "On Break" : "Working",
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isOnBreak ? Colors.orange[700] : Colors.green[700],
            ),
          ),
          const SizedBox(height: 8),
          StreamBuilder<int>(
            stream: _stopWatchTimer.rawTime,
            initialData: 0,
            builder: (context, snap) {
              final value = snap.data!;
              final displayTime = StopWatchTimer.getDisplayTime(
                value,
                hours: true,
              );
              return Text(
                displayTime,
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isOnBreak ? Colors.orange[700] : Colors.green[700],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPunchInButton(Color themeColor) {
    return GestureDetector(
      onTap: _openFaceRecognition,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [themeColor, themeColor.withValues(alpha: 0.7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: themeColor.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.face_retouching_natural,
              color: Colors.white,
              size: 48,
            ),
            const SizedBox(height: 12),
            Text(
              "PUNCH IN",
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreakAndPunchOutButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _toggleBreak,
            style: ElevatedButton.styleFrom(
              backgroundColor: isOnBreak ? Colors.orange : Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
            ),
            icon: Icon(
              isOnBreak ? Icons.play_arrow : Icons.free_breakfast,
              color: Colors.white,
            ),
            label: Text(
              isOnBreak ? "End Break" : "Take Break",
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _punchOut,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
            ),
            icon: const Icon(Icons.logout, color: Colors.white),
            label: Text(
              "Punch Out",
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusMessage(Color themeColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: isPunchedIn
            ? (isOnBreak ? Colors.orange[50] : Colors.green[50])
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: isPunchedIn
              ? (isOnBreak ? Colors.orange[200]! : Colors.green[200]!)
              : Colors.grey[300]!,
        ),
      ),
      child: Text(
        !isPunchedIn
            ? "👋 Ready to start your work day?"
            : isOnBreak
            ? "☕ Enjoy your break!"
            : "💪 Keep up the great work!",
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: isPunchedIn
              ? (isOnBreak ? Colors.orange[700] : Colors.green[700])
              : themeColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
