import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/models/attendance_models.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final themeColor = const Color(0xFF561c24);
  String? selectedMonth; // Track which month is selected for daily view

  // Sample monthly attendance data
  final List<MonthlyAttendance> monthlyData = [
    MonthlyAttendance(
      month: 'Jan 2023',
      daysPresent: 27,
      totalWorkingDays: 27,
      officialLeaves: 4,
    ),
    MonthlyAttendance(
      month: 'Dec 2022',
      daysPresent: 25,
      totalWorkingDays: 26,
      officialLeaves: 1,
    ),
    MonthlyAttendance(
      month: 'Nov 2022',
      daysPresent: 24,
      totalWorkingDays: 25,
      officialLeaves: 1,
    ),
  ];

  // Sample daily attendance data for January 2023
  final List<DailyAttendance> dailyData = [
    DailyAttendance(
      date: DateTime(2023, 1, 8),
      dayOfWeek: 'Mon',
      checkIn: '9:35 AM',
      checkOut: '5:30 PM',
      isWorkingDay: true,
    ),
    DailyAttendance(
      date: DateTime(2023, 1, 9),
      dayOfWeek: 'Tue',
      checkIn: '9:40 AM',
      checkOut: '5:25 PM',
      isWorkingDay: true,
    ),
    DailyAttendance(
      date: DateTime(2023, 1, 10),
      dayOfWeek: 'Wed',
      checkIn: '9:30 AM',
      checkOut: '5:35 PM',
      isWorkingDay: true,
    ),
    DailyAttendance(
      date: DateTime(2023, 1, 11),
      dayOfWeek: 'Thu',
      checkIn: '--',
      checkOut: '--',
      isWorkingDay: false,
    ),
    DailyAttendance(
      date: DateTime(2023, 1, 12),
      dayOfWeek: 'Fri',
      checkIn: '9:45 AM',
      checkOut: '5:20 PM',
      isWorkingDay: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: themeColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'MY ATTENDANCE',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // White content area with curved top
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                Expanded(
                  child: selectedMonth == null
                      ? _buildMonthlyView()
                      : _buildDailyView(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthlyView() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: monthlyData.length,
      itemBuilder: (context, index) {
        final data = monthlyData[index];
        return _buildMonthlyCard(data);
      },
    );
  }

  Widget _buildMonthlyCard(MonthlyAttendance data) {
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedMonth = data.month;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 20),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey[200]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  data.month,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                // Days Present Circle
                Container(
                  width: 77,
                  height: 77,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: themeColor, width: 3.5),
                    color: Colors.white,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        data.daysPresent.toString(),
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: themeColor,
                        ),
                      ),
                      Text(
                        'Days Present',
                        style: GoogleFonts.poppins(
                          fontSize: 9,
                          fontWeight: FontWeight.w600,
                          color: themeColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Working Days and Leaves
                Expanded(
                  child: Column(
                    children: [
                      _buildInfoRow(
                        'Total Working Days',
                        '${data.totalWorkingDays} Days',
                        themeColor,
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow(
                        'Official Leaves',
                        '${data.officialLeaves} Days',
                        Colors.grey[600]!,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, Color color) {
    return Row(
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 10.5,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDailyView() {
    return Column(
      children: [
        // Month Header with Back Button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    selectedMonth = null;
                  });
                },
                icon: Icon(Icons.arrow_back, color: Colors.grey[600]),
              ),
              Text(
                selectedMonth ?? 'Jan 2023',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  // Previous month
                },
                icon: Icon(Icons.chevron_left, color: Colors.grey[600]),
              ),
              IconButton(
                onPressed: () {
                  // Next month
                },
                icon: Icon(Icons.chevron_right, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        // Daily List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemCount: dailyData.length,
            itemBuilder: (context, index) {
              final data = dailyData[index];
              return _buildDailyCard(data);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDailyCard(DailyAttendance data) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Date Square
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: data.isWorkingDay ? themeColor : Colors.grey[400],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  data.date.day.toString(),
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  data.dayOfWeek,
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Check In/Out Times
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Check In',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      data.checkIn,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'Check Out',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      data.checkOut,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


