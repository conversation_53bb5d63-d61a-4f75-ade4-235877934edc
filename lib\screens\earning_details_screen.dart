import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/models/payroll_models.dart';
import 'package:lucent_employee_app/widgets/payroll_widgets.dart';

class EarningDetailsScreen extends StatefulWidget {
  const EarningDetailsScreen({super.key});

  @override
  State<EarningDetailsScreen> createState() => _EarningDetailsScreenState();
}

class _EarningDetailsScreenState extends State<EarningDetailsScreen> {
  final themeColor = const Color(0xFF561c24);

  // Sample earning details data
  final EarningDetails earningDetails = EarningDetails(
    details: [
      EarningDetail(component: 'Basic Pay', amount: 2000.00),
      EarningDetail(component: 'HRA', amount: 500.00),
      EarningDetail(component: 'Other Allowance', amount: 500.00),
      EarningDetail(component: 'SPL Allowance', amount: 680.00),
    ],
    totalEarning: 3680.00,
  );

  final PayslipSummary payslipData = PayslipSummary(
    netPay: '3680.35',
    period: 'Jan 15th-Jan 25th, 2023',
    grossPay: 4680.0,
    earnings: 3680.35,
    taxAndDeductions: 1000.0,
    totalHours: 56,
    nextPaycheck: 'Jan 31',
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: themeColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'PAYSLIP SUMMARY',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // Net pay display section
        Container(
          width: double.infinity,
          color: themeColor,
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
          child: Column(
            children: [
              Text(
                '\$${payslipData.netPay}',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Net Pay, ${payslipData.period}',
                style: GoogleFonts.poppins(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
        // White content area
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildDetailsButton(),
                  const SizedBox(height: 20),
                  _buildGrossPayChart(),
                  const SizedBox(height: 20),
                  _buildEarningDetailsSection(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsButton() {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        color: themeColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: themeColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'Details',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildGrossPayChart() {
    return PayrollChart(
      grossPay: payslipData.grossPay,
      earnings: payslipData.earnings,
      taxAndDeductions: payslipData.taxAndDeductions,
      themeColor: themeColor,
    );
  }

  Widget _buildEarningDetailsSection() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'EARNING DETAILS',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: earningDetails.details.length + 1, // +1 for total
              itemBuilder: (context, index) {
                if (index == earningDetails.details.length) {
                  // Total earning row
                  return Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: themeColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: themeColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total Earning',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: themeColor,
                          ),
                        ),
                        Text(
                          '\$${earningDetails.totalEarning.toStringAsFixed(2)}',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: themeColor,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final detail = earningDetails.details[index];
                return PayrollCard(
                  title: detail.component,
                  value: '\$${detail.amount.toStringAsFixed(2)}',
                );
              },
            ),
          ),
        ],
      ),
    );
  }

}
