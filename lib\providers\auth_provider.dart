import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';

// AuthService provider (depends on baseUrlProvider)
final authServiceProvider = Provider.family<AuthService, String>((
  ref,
  baseUrl,
) {
  if (baseUrl.isEmpty) {
    throw Exception("Base URL not set");
  }
  return AuthService(baseUrl);
});

// StateNotifier for authentication
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService authService;
  final String baseUrl;

  AuthNotifier(this.authService, this.baseUrl) : super(AuthState()) {
    _loadStoredAuth();
  }

  // Load stored authentication data on initialization
  Future<void> _loadStoredAuth() async {
    final isLoggedIn = await StorageService.isLoggedIn();
    if (isLoggedIn) {
      final storedData = await StorageService.getStoredAuthData();
      final storedUrl = storedData['companyUrl'];

      // Only load if the stored URL matches the current baseUrl
      if (storedUrl == baseUrl) {
        state = state.copyWith(
          token: storedData['token'],
          userEmail: storedData['userEmail'],
          companyName: storedData['companyName'],
        );
      }
    }
  }

  Future<void> login(
    String email,
    String password, {
    String? companyName,
  }) async {
    // Prevent multiple simultaneous login attempts
    if (state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
    ); // Clear any previous error
    try {
      final data = await authService.login(email, password);
      final token = data["token"];

      // Save authentication data to storage
      await StorageService.saveAuthData(
        token: token,
        companyName: companyName ?? '',
        companyUrl: baseUrl,
        userEmail: email,
      );

      state = state.copyWith(
        isLoading: false,
        token: token,
        userEmail: email,
        companyName: companyName,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> logout() async {
    await StorageService.clearAuthData();
    state = AuthState(); // Reset to initial state
  }

  void clearError() {
    if (!state.isLoading) {
      // Only clear error if not currently loading
      state = state.copyWith(error: null);
    }
  }
}

final authNotifierProvider =
    StateNotifierProvider.family<AuthNotifier, AuthState, String>((
      ref,
      baseUrl,
    ) {
      final service = ref.watch(authServiceProvider(baseUrl));
      return AuthNotifier(service, baseUrl);
    });

// Authentication State
class AuthState {
  final bool isLoading;
  final String? token;
  final String? error;
  final String? userEmail;
  final String? companyName;

  AuthState({
    this.isLoading = false,
    this.token,
    this.error,
    this.userEmail,
    this.companyName,
  });

  AuthState copyWith({
    bool? isLoading,
    String? token,
    String? error,
    String? userEmail,
    String? companyName,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      token: token ?? this.token,
      error: error, // Allow explicit null for error
      userEmail: userEmail ?? this.userEmail,
      companyName: companyName ?? this.companyName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.isLoading == isLoading &&
        other.token == token &&
        other.error == error &&
        other.userEmail == userEmail &&
        other.companyName == companyName;
  }

  @override
  int get hashCode =>
      isLoading.hashCode ^
      token.hashCode ^
      error.hashCode ^
      userEmail.hashCode ^
      companyName.hashCode;
}
