import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucent_employee_app/screens/company_select_screen.dart';
import 'package:lucent_employee_app/screens/dashboard.dart';
// import 'package:lucent_employee_app/screens/home_screen.dart';
import 'package:lucent_employee_app/services/storage_service.dart';
import 'package:lucent_employee_app/providers/auth_provider.dart';

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Small delay to show splash screen
  await Future.delayed(const Duration(milliseconds: 1000));
  FlutterNativeSplash.remove();

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Lucent Employee App',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const AuthWrapper(),
    );
  }
}

// Wrapper widget that handles authentication check and routing
class AuthWrapper extends ConsumerStatefulWidget {
  const AuthWrapper({super.key});

  @override
  ConsumerState<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends ConsumerState<AuthWrapper> {
  bool _isLoading = true;
  Widget _targetScreen = const CompanySelectScreen();

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      // Check if user is logged in
      final isLoggedIn = await StorageService.isLoggedIn();

      if (isLoggedIn) {
        // Get stored auth data
        final storedData = await StorageService.getStoredAuthData();
        final companyUrl = storedData['companyUrl'];
        final token = storedData['token'];

        if (companyUrl != null &&
            companyUrl.isNotEmpty &&
            token != null &&
            token.isNotEmpty) {
          // Initialize auth provider with stored data
          ref.read(authNotifierProvider(companyUrl).notifier);

          // Wait for auth state to load
          await Future.delayed(const Duration(milliseconds: 300));

          // Check if auth state has valid token
          final authState = ref.read(authNotifierProvider(companyUrl));

          if (authState.token != null && authState.token!.isNotEmpty) {
            _targetScreen = const Dashboard();
          }
        }
      }
    } catch (e) {
      // On any error, default to company select screen
      _targetScreen = const CompanySelectScreen();
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      // Show loading indicator while checking auth
      return const Scaffold(
        backgroundColor: Color(0xFF561c24),
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    return _targetScreen;
  }
}
