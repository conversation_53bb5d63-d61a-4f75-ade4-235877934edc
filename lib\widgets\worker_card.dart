import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/models/worker_attendance_models.dart';
import 'package:lucent_employee_app/providers/worker_attendance_provider.dart';
import 'package:lucent_employee_app/screens/worker_camera_screen.dart';

class WorkerCard extends ConsumerWidget {
  final Worker worker;
  final themeColor = const Color(0xFF561c24);

  const WorkerCard({super.key, required this.worker});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final attendance = ref.watch(workerAttendanceByIdProvider(worker.id));

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Worker info and photo row
          Row(
            children: [
              // Worker photo
              _buildWorkerPhoto(attendance),
              const SizedBox(width: 16),

              // Worker details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      worker.name,
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      worker.designation,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildStatusChip(attendance),
                  ],
                ),
              ),

              // Timer display
              if (attendance?.isPunchedIn == true)
                _buildTimerDisplay(attendance!),
            ],
          ),

          const SizedBox(height: 16),

          // Action buttons
          _buildActionButtons(context, ref, attendance),
        ],
      ),
    );
  }

  Widget _buildWorkerPhoto(WorkerAttendance? attendance) {
    String? imagePath;

    // Show punch in photo if available, otherwise show profile photo
    if (attendance?.punchInImagePath != null) {
      imagePath = attendance!.punchInImagePath;
    } else if (worker.profileImagePath != null) {
      imagePath = worker.profileImagePath;
    }

    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[200],
        border: Border.all(
          color: attendance?.isPunchedIn == true ? Colors.green : themeColor,
          width: 3,
        ),
      ),
      child: ClipOval(
        child: imagePath != null && File(imagePath).existsSync()
            ? Image.file(File(imagePath), fit: BoxFit.cover)
            : Icon(Icons.person, size: 40, color: Colors.grey[600]),
      ),
    );
  }

  Widget _buildStatusChip(WorkerAttendance? attendance) {
    Color chipColor;
    String statusText;
    IconData icon;

    if (attendance == null ||
        attendance.status == AttendanceStatus.notPunchedIn) {
      chipColor = Colors.grey;
      statusText = 'Not Punched In';
      icon = Icons.schedule;
    } else if (attendance.status == AttendanceStatus.punchedIn) {
      chipColor = Colors.green;
      statusText = 'Punched In';
      icon = Icons.check_circle;
    } else if (attendance.status == AttendanceStatus.onBreak) {
      chipColor = Colors.orange;
      statusText = 'On Break';
      icon = Icons.free_breakfast;
    } else {
      chipColor = Colors.blue;
      statusText = 'Punched Out';
      icon = Icons.logout;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.white),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimerDisplay(WorkerAttendance attendance) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: themeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: themeColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(Icons.timer, color: themeColor, size: 20),
          const SizedBox(height: 4),
          Text(
            attendance.formattedWorkDuration,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: themeColor,
            ),
          ),
          Text(
            'Work Time',
            style: GoogleFonts.poppins(fontSize: 10, color: themeColor),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    WidgetRef ref,
    WorkerAttendance? attendance,
  ) {
    if (attendance == null ||
        attendance.status == AttendanceStatus.notPunchedIn) {
      // Show punch in button
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () => _handlePunchIn(context, ref),
          style: ElevatedButton.styleFrom(
            backgroundColor: themeColor,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          icon: const Icon(Icons.login, color: Colors.white),
          label: Text(
            'Punch In',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
      );
    } else if (attendance.status == AttendanceStatus.punchedOut) {
      // Show completed status
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.check_circle, color: Colors.blue),
            const SizedBox(width: 8),
            Text(
              'Work Completed',
              style: GoogleFonts.poppins(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    } else {
      // Show break and punch out buttons
      return Row(
        children: [
          // Break button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _handleBreakToggle(ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: attendance.isOnBreak
                    ? Colors.orange
                    : Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: Icon(
                attendance.isOnBreak ? Icons.play_arrow : Icons.free_breakfast,
                color: Colors.white,
                size: 18,
              ),
              label: Text(
                attendance.isOnBreak ? 'End Break' : 'Take Break',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Punch out button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _handlePunchOut(context, ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: const Icon(Icons.logout, color: Colors.white, size: 18),
              label: Text(
                'Punch Out',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  void _handlePunchIn(BuildContext context, WidgetRef ref) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkerCameraScreen(
          workerName: worker.name,
          action: 'punch_in',
          onPhotoTaken: (imagePath) {
            ref
                .read(workerAttendanceProvider.notifier)
                .punchIn(worker.id, imagePath);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '${worker.name} punched in successfully!',
                  style: GoogleFonts.poppins(),
                ),
                backgroundColor: Colors.green,
              ),
            );
          },
        ),
      ),
    );
  }

  void _handlePunchOut(BuildContext context, WidgetRef ref) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkerCameraScreen(
          workerName: worker.name,
          action: 'punch_out',
          onPhotoTaken: (imagePath) {
            ref
                .read(workerAttendanceProvider.notifier)
                .punchOut(worker.id, imagePath);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '${worker.name} punched out successfully!',
                  style: GoogleFonts.poppins(),
                ),
                backgroundColor: Colors.blue,
              ),
            );
          },
        ),
      ),
    );
  }

  void _handleBreakToggle(WidgetRef ref) {
    ref.read(workerAttendanceProvider.notifier).toggleBreak(worker.id);
  }
}
