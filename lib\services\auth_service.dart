import 'dart:convert';
import 'package:http/http.dart' as http;

class AuthService {
  final String baseUrl;
  AuthService(this.baseUrl);

  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final url = Uri.parse('$baseUrl/auth/login');
      final response = await http.post(
        url,
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"email": email, "password": password}),
      );

      // First try to decode the response body
      Map<String, dynamic> data;
      try {
        data = jsonDecode(response.body);
      } catch (e) {
        throw Exception("Server returned invalid response. Please try again.");
      }

      if (response.statusCode == 200 && data["status"] == true) {
        return data; // success
      } else if (response.statusCode == 401) {
        throw Exception("Invalid email or password");
      } else if (response.statusCode == 400) {
        throw Exception(
          data["message"] ?? "Invalid request. Please check your inputs.",
        );
      } else {
        throw Exception(data["message"] ?? "<PERSON><PERSON> failed. Please try again.");
      }
    } catch (e) {
      if (e is Exception) {
        rethrow; // Rethrow already formatted exceptions
      }
      // Check if it's a connection error
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable')) {
        throw Exception(
          "Unable to connect to server. Please check your internet connection.",
        );
      }
      throw Exception("An unexpected error occurred. Please try again.");
    }
  }
}
