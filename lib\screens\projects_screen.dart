import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lucent_employee_app/models/project_models.dart';
import 'package:lucent_employee_app/screens/projects_tasks_screen.dart' hide Project;
class ProjectsScreen extends StatelessWidget {
  const ProjectsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeColor = const Color(0xFF561c24);

    // Temporary mock data
    final projects = [
      Project(
        id: '1',
        projectNumber: '12356',
        name: 'Building Construction',
        type: 'Construction',
        startDate: DateTime(2023, 4, 20),
        endDate: DateTime(2023, 5, 20),
        teamMembers: [
          TeamMember(id: '1', name: '<PERSON>', role: 'Lead'),
          TeamMember(id: '2', name: '<PERSON>', role: 'Engineer'),
          TeamMember(id: '3', name: '<PERSON>', role: 'Worker'),
        ],
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text('Projects', style: GoogleFonts.poppins()),
        backgroundColor: themeColor,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: projects.length,
        itemBuilder: (context, index) {
          final project = projects[index];
          return _buildProjectCard(context, project, themeColor);
        },
      ),
      backgroundColor: const Color(0xFFF7F6F9),
    );
  }

  Widget _buildProjectCard(BuildContext context, Project project, Color themeColor) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProjectTasksScreen(project: project),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [themeColor.withOpacity(0.9), themeColor.withOpacity(0.7)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Project #${project.projectNumber}",
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        project.name,
                        style: GoogleFonts.poppins(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Text(
                      project.type,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Body
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      _buildDateInfo(
                        'Start',
                        DateFormat('dd MMM yyyy').format(project.startDate),
                        themeColor,
                        Icons.play_circle_fill,
                      ),
                      const SizedBox(width: 20),
                      _buildDateInfo(
                        'End',
                        DateFormat('dd MMM yyyy').format(project.endDate),
                        themeColor,
                        Icons.flag_circle,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Team members
                  Row(
                    children: [
                      Text(
                        'Team',
                        style: GoogleFonts.poppins(
                          color: Colors.grey[700],
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: SizedBox(
                          height: 36,
                          child: Stack(
                            children: project.teamMembers
                                .asMap()
                                .entries
                                .map(
                                  (entry) => Positioned(
                                    left: entry.key * 22.0,
                                    child: CircleAvatar(
                                      radius: 18,
                                      backgroundColor: themeColor.withOpacity(0.1),
                                      child: Text(
                                        entry.value.name[0],
                                        style: GoogleFonts.poppins(
                                          color: themeColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateInfo(String label, String date, Color themeColor, IconData icon) {
    return Expanded(
      child: Row(
        children: [
          Icon(icon, color: themeColor, size: 20),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              Text(
                date,
                style: GoogleFonts.poppins(
                  color: themeColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
