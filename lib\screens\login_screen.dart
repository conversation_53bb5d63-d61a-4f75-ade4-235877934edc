import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/providers/auth_provider.dart';
import 'package:lucent_employee_app/screens/home_screen.dart';

class LoginScreen extends ConsumerStatefulWidget {
  final String baseUrl;
  final String? companyName;
  const LoginScreen(this.baseUrl, {this.companyName, super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  bool _obscurePassword = true;
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  String _loginError = '';
  String _passwordError = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _loginController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _validateAndLogin() {
    // Prevent multiple calls while loading
    final authState = ref.read(authNotifierProvider(widget.baseUrl));
    if (authState.isLoading) return;

    setState(() {
      _loginError = '';
      _passwordError = '';
    });

    final login = _loginController.text.trim();
    final password = _passwordController.text;

    if (login.isEmpty) {
      setState(() {
        _loginError = 'Please enter your email';
      });
      return;
    }

    if (password.isEmpty) {
      setState(() {
        _passwordError = 'Please enter your password';
      });
      return;
    }

    // Attempt login
    ref
        .read(authNotifierProvider(widget.baseUrl).notifier)
        .login(login, password, companyName: widget.companyName);
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider(widget.baseUrl));

    // Listen to auth state changes for navigation and error handling
    ref.listen<AuthState>(authNotifierProvider(widget.baseUrl), (
      previous,
      next,
    ) {
      // Show error Snackbar once - only if error is new
      if (next.error != null &&
          next.error!.isNotEmpty &&
          previous?.error != next.error) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              next.error!.replaceAll("Exception: ", ""),
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
          ),
        );
        // Clear error after a delay to prevent immediate re-triggering
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            ref
                .read(authNotifierProvider(widget.baseUrl).notifier)
                .clearError();
          }
        });
      }

      // Navigate on success
      if (next.token != null &&
          next.token!.isNotEmpty &&
          (previous?.token == null || (previous?.token?.isEmpty ?? true))) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (_) => const HomeScreen()),
        );
      }
    });

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFE8D8C4), Color(0xFF561c24)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: _buildLoginForm(authState),
      ),
    );
  }

  Widget _buildLoginForm(AuthState authState) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const SizedBox(height: 80),
          // Logo / Title
          Image.asset('assets/images/lucent_erp2.png', height: 180, width: 180),
          // const SizedBox(height: 10),
          Text(
            "Welcome Back!",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            "Login to your account",
            style: GoogleFonts.poppins(color: Colors.white70, fontSize: 16),
          ),
          const SizedBox(height: 20),

          // Glassmorphic Card
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.85),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              children: [
                // Email  field
                TextField(
                  controller: _loginController,
                  decoration: InputDecoration(
                    labelText: "Email",
                    hintText: "Enter your email",
                    prefixIcon: const Icon(Icons.person_outline),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF561c24),
                        width: 2.0,
                      ),
                    ),
                    errorText: _loginError.isNotEmpty ? _loginError : null,
                    floatingLabelStyle: const TextStyle(
                      color: Color(0xFF561c24),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Password field
                TextField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: "Password",
                    hintText: "Enter your password",
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF561c24),
                        width: 2.0,
                      ),
                    ),
                    errorText: _passwordError.isNotEmpty
                        ? _passwordError
                        : null,
                    floatingLabelStyle: const TextStyle(
                      color: Color(0xFF561c24),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                const SizedBox(height: 30),

                // Login Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF561c24),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onPressed: authState.isLoading ? null : _validateAndLogin,
                    child: authState.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            "LOGIN",
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 15),

                // // Forgot Password
                // Align(
                //   alignment: Alignment.centerRight,
                //   child: TextButton(
                //     onPressed: () {
                //       Navigator.push(context, MaterialPageRoute(builder: (context)=> const ForgotPasswordRequestScreen()));
                //     },
                //     child: const Text(
                //       "Forgot Password?",
                //       style: TextStyle(color: Color(0xFF561c24)),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),

          // const SizedBox(height: 20),
        ],
      ),
    );
  }
}
