import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class McSubmissionScreen extends StatelessWidget {
  const McSubmissionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeColor = const Color(0xFF561c24);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: themeColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          "MC Submission",
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        actions: const [
          Padding(
            padding: EdgeInsets.only(right: 16.0),
            child: Icon(Icons.notifications, color: Colors.white),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Illustration placeholder (replace with your image asset if needed)
            SizedBox(
              height: 180,
              child: Center(
                child: Image.asset(
                  "assets/images/illustration.png", // replace with your asset
                  fit: BoxFit.contain,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Upload PDF
            _buildUploadCard(
              context,
              title: "Upload PDF",
              buttonText: "Choose Your File",
              icon: Icons.upload_file,
              themeColor: themeColor,
              onTap: () {},
            ),
            const SizedBox(height: 16),

            // Upload Photos
            _buildUploadCard(
              context,
              title: "Upload Photos",
              buttonText: "Choose Your File",
              icon: Icons.photo_library_outlined,
              themeColor: themeColor,
              onTap: () {},
            ),
            const SizedBox(height: 16),

            // Choose Date
            _buildUploadCard(
              context,
              title: "Date",
              buttonText: "Choose Your Date",
              icon: Icons.calendar_today,
              themeColor: themeColor,
              onTap: () async {
                final pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime(2100),
                );
                if (pickedDate != null) {
                  // handle selected date
                }
              },
            ),
            const SizedBox(height: 30),

            // Submit button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: themeColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 3,
                ),
                child: Text(
                  "SUBMIT",
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadCard(
    BuildContext context, {
    required String title,
    required String buttonText,
    required IconData icon,
    required Color themeColor,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black12.withOpacity(0.05),
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: onTap,
                  style: TextButton.styleFrom(alignment: Alignment.centerLeft),
                  child: Text(
                    buttonText,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
              CircleAvatar(
                radius: 20,
                backgroundColor: themeColor.withOpacity(0.1),
                child: Icon(icon, color: themeColor),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
