import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/services/camera_service.dart';

class WorkerCameraScreen extends StatefulWidget {
  final String workerName;
  final String action; // 'punch_in' or 'punch_out'
  final Function(String imagePath) onPhotoTaken;

  const WorkerCameraScreen({
    super.key,
    required this.workerName,
    required this.action,
    required this.onPhotoTaken,
  });

  @override
  State<WorkerCameraScreen> createState() => _WorkerCameraScreenState();
}

class _WorkerCameraScreenState extends State<WorkerCameraScreen> {
  final themeColor = const Color(0xFF561c24);
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _capturedImagePath;
  bool _showPreview = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    setState(() => _isLoading = true);

    final initialized = await CameraService.initializeCamera();

    if (mounted) {
      setState(() {
        _isInitialized = initialized;
        _isLoading = false;
      });

      if (!initialized) {
        _showErrorAndClose('Failed to initialize camera');
      }
    }
  }

  void _showErrorAndClose(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.poppins()),
        backgroundColor: Colors.red,
      ),
    );
    Navigator.of(context).pop();
  }

  Future<void> _takePicture() async {
    if (!_isInitialized) return;

    setState(() => _isLoading = true);

    try {
      final imagePath = await CameraService.takePicture(
        fileName:
            '${widget.workerName}_${widget.action}_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      if (imagePath != null) {
        setState(() {
          _capturedImagePath = imagePath;
          _showPreview = true;
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
        _showErrorAndClose('Failed to capture photo');
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorAndClose('Error capturing photo: $e');
    }
  }

  void _retakePicture() {
    setState(() {
      _showPreview = false;
      _capturedImagePath = null;
    });
  }

  void _confirmPhoto() {
    if (_capturedImagePath != null) {
      widget.onPhotoTaken(_capturedImagePath!);
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    // Don't dispose the camera service here as it might be used by other screens
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '${widget.action == 'punch_in' ? 'Punch In' : 'Punch Out'} Photo',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.white))
          : _showPreview
          ? _buildPreviewScreen()
          : _buildCameraScreen(),
    );
  }

  Widget _buildCameraScreen() {
    if (!_isInitialized || CameraService.controller == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.camera_alt, color: Colors.white, size: 64),
            const SizedBox(height: 16),
            Text(
              'Camera not available',
              style: GoogleFonts.poppins(color: Colors.white, fontSize: 18),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // Camera preview
        Positioned.fill(child: CameraPreview(CameraService.controller!)),

        // Worker info overlay
        Positioned(
          top: 20,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  widget.workerName,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.action == 'punch_in' ? 'Punching In' : 'Punching Out',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Instructions
        Positioned(
          bottom: 120,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Position the worker\'s face in the center and tap the capture button',
              style: GoogleFonts.poppins(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        // Capture button
        Positioned(
          bottom: 30,
          left: 0,
          right: 0,
          child: Center(
            child: GestureDetector(
              onTap: _takePicture,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: themeColor,
                  border: Border.all(color: Colors.white, width: 4),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewScreen() {
    if (_capturedImagePath == null) return const SizedBox();

    return Stack(
      children: [
        // Image preview
        Positioned.fill(
          child: Image.file(File(_capturedImagePath!), fit: BoxFit.cover),
        ),

        // Worker info overlay
        Positioned(
          top: 20,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  widget.workerName,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Photo Preview',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Action buttons
        Positioned(
          bottom: 30,
          left: 20,
          right: 20,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Retake button
              ElevatedButton.icon(
                onPressed: _retakePicture,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[800],
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: Text(
                  'Retake',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Confirm button
              ElevatedButton.icon(
                onPressed: _confirmPhoto,
                style: ElevatedButton.styleFrom(
                  backgroundColor: themeColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                icon: const Icon(Icons.check, color: Colors.white),
                label: Text(
                  'Confirm',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
