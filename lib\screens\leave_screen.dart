import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lucent_employee_app/models/leaves_models.dart';
import 'package:lucent_employee_app/screens/new_leave_screen.dart';

class LeaveScreen extends StatefulWidget {
  const LeaveScreen({super.key});

  @override
  State<LeaveScreen> createState() => _LeaveScreenState();
}

class _LeaveScreenState extends State<LeaveScreen> {
  String selectedFilter = 'All';
  final themeColor = const Color(0xFF561c24);

  // Sample leave data - in a real app, this would come from an API
  final List<LeaveRequest> leaveRequests = [
    LeaveRequest(
      id: '95 259105',
      reason: 'Going for a Family Trip',
      category: 'Casual Leave',
      status: 'Awaiting',
      startDate: DateTime(2023, 3, 20),
      endDate: DateTime(2023, 3, 29),
      manager: 'Manager <PERSON>',
    ),
    LeaveRequest(
      id: '95 259106',
      reason: 'Going for a Reception Party',
      category: 'Casual Leave',
      status: 'Declined',
      startDate: DateTime(2023, 2, 25),
      endDate: DateTime(2023, 2, 25),
      manager: 'Manager <PERSON>o',
    ),
    LeaveRequest(
      id: '95 259107',
      reason: 'Sick',
      category: 'Sick Leave',
      status: 'Approved',
      startDate: DateTime(2023, 2, 5),
      endDate: DateTime(2023, 2, 5),
      manager: 'Manager Johan Deo',
    ),
    LeaveRequest(
      id: '95 259108',
      reason: 'Going for a Trip',
      category: 'Casual Leave',
      status: 'Approved',
      startDate: DateTime(2023, 1, 2),
      endDate: DateTime(2023, 1, 5),
      manager: 'Manager Johan Deo',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: themeColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'LEAVES',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // White content area with curved top
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                _buildFilterButtons(),
                const SizedBox(height: 20),
                Expanded(child: _buildLeaveList()),
              ],
            ),
          ),
        ),
      ],
    );
  }


  Widget _buildFilterButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          _buildFilterButton('All'),
          const SizedBox(width: 12),
          _buildFilterButton('Casual'),
          const SizedBox(width: 12),
          _buildFilterButton('Sick'),
          const SizedBox(width: 20),
           Container(
            decoration: BoxDecoration(
              color: themeColor,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: themeColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: IconButton(
              onPressed: () {
                // Navigate to new leave form
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NewLeaveScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.add, color: Colors.white, size: 24),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(String filter) {
    final isSelected = selectedFilter == filter;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedFilter = filter;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? themeColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          filter,
          style: GoogleFonts.poppins(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildLeaveList() {
    final filteredRequests = selectedFilter == 'All'
        ? leaveRequests
        : leaveRequests.where((request) {
            if (selectedFilter == 'Casual') {
              return request.category == 'Casual Leave';
            } else if (selectedFilter == 'Sick') {
              return request.category == 'Sick Leave';
            }
            return true;
          }).toList();

    // Group by month
    final groupedRequests = <String, List<LeaveRequest>>{};
    for (final request in filteredRequests) {
      final monthKey = DateFormat('MMM yyyy').format(request.startDate);
      groupedRequests.putIfAbsent(monthKey, () => []).add(request);
    }

    // Sort months in descending order
    final sortedMonths = groupedRequests.keys.toList()
      ..sort(
        (a, b) => DateFormat(
          'MMM yyyy',
        ).parse(b).compareTo(DateFormat('MMM yyyy').parse(a)),
      );

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: sortedMonths.length,
      itemBuilder: (context, index) {
        final month = sortedMonths[index];
        final requests = groupedRequests[month]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 20, bottom: 12),
              child: Text(
                month.toUpperCase(),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ...requests.map((request) => _buildLeaveCard(request)),
          ],
        );
      },
    );
  }

  Widget _buildLeaveCard(LeaveRequest request) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'ID: ${request.id}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: themeColor,
                ),
              ),
              _buildStatusChip(request.status),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            request.reason,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            request.category,
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Text(
                'Leave Date',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Text(
                _formatDateRange(request.startDate, request.endDate),
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              CircleAvatar(
                radius: 8,
                backgroundColor: Colors.grey[300],
                child: Icon(Icons.person, size: 10, color: Colors.grey[600]),
              ),
              const SizedBox(width: 6),
              Text(
                request.manager,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Icon(Icons.arrow_forward_ios, size: 12, color: Colors.grey[400]),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    switch (status) {
      case 'Approved':
        backgroundColor = themeColor;
        break;
      case 'Declined':
        backgroundColor = Colors.red;
        break;
      case 'Awaiting':
        backgroundColor = Colors.orange;
        break;
      default:
        backgroundColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: GoogleFonts.poppins(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }


  String _formatDateRange(DateTime startDate, DateTime endDate) {
    if (startDate.day == endDate.day &&
        startDate.month == endDate.month &&
        startDate.year == endDate.year) {
      return '${startDate.day}${_getOrdinalSuffix(startDate.day)} ${DateFormat('MMM').format(startDate)}';
    } else {
      return '${startDate.day}${_getOrdinalSuffix(startDate.day)} ${DateFormat('MMM').format(startDate)} to ${endDate.day}${_getOrdinalSuffix(endDate.day)} ${DateFormat('MMM').format(endDate)}';
    }
  }

  String _getOrdinalSuffix(int day) {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}





