import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/models/payroll_models.dart';
import 'package:lucent_employee_app/screens/earning_details_screen.dart';
import 'package:lucent_employee_app/widgets/payroll_widgets.dart';

class PayslipSummaryScreen extends StatefulWidget {
  const PayslipSummaryScreen({super.key});

  @override
  State<PayslipSummaryScreen> createState() => _PayslipSummaryScreenState();
}

class _PayslipSummaryScreenState extends State<PayslipSummaryScreen> {
  final themeColor = const Color(0xFF561c24);

  // Sample payslip data
  final PayslipSummary payslipData = PayslipSummary(
    netPay: '3680.35',
    period: 'Jan 15th-Jan 25th, 2023',
    grossPay: 4680.0,
    earnings: 3680.35,
    taxAndDeductions: 1000.0,
    totalHours: 56,
    nextPaycheck: 'Jan 31',
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: themeColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'PAYSLIP SUMMARY',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // Net pay display section
        Container(
          width: double.infinity,
          color: themeColor,
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
          child: Column(
            children: [
              Text(
                '${payslipData.netPay}',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Net Pay, ${payslipData.period}',
                style: GoogleFonts.poppins(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
        // White content area
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildDetailsButton(),
                  const SizedBox(height: 20),
                  _buildGrossPayChart(),
                  const SizedBox(height: 20),
                  Expanded(child: _buildSummaryDetails()),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsButton() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EarningDetailsScreen()),
        );
      },
      child: Container(
        width: double.infinity,
        height: 50,
        decoration: BoxDecoration(
          color: themeColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: themeColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Text(
            'Details',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGrossPayChart() {
    return PayrollChart(
      grossPay: payslipData.grossPay,
      earnings: payslipData.earnings,
      taxAndDeductions: payslipData.taxAndDeductions,
      themeColor: themeColor,
    );
  }

  Widget _buildSummaryDetails() {
    return Column(
      children: [
        PayrollCard(title: 'Total Hours', value: '${payslipData.totalHours} h'),
        const SizedBox(height: 12),
        PayrollCard(
          title: 'Total Earnings',
          value: '${payslipData.grossPay.toStringAsFixed(2)}',
          valueColor: themeColor,
        ),
        const SizedBox(height: 12),
        PayrollCard(
          title: 'Taxes & Deductions',
          value: '${payslipData.taxAndDeductions.toStringAsFixed(2)}',
          valueColor: Colors.red[600],
        ),
        const SizedBox(height: 12),
        PayrollCard(title: 'Next Paycheck', value: payslipData.nextPaycheck),
      ],
    );
  }


}
