// Worker Attendance Models
class Worker {
  final String id;
  final String name;
  final String designation;
  final String? profileImagePath;
  final bool isActive;

  Worker({
    required this.id,
    required this.name,
    required this.designation,
    this.profileImagePath,
    this.isActive = true,
  });

  Worker copyWith({
    String? id,
    String? name,
    String? designation,
    String? profileImagePath,
    bool? isActive,
  }) {
    return Worker(
      id: id ?? this.id,
      name: name ?? this.name,
      designation: designation ?? this.designation,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      isActive: isActive ?? this.isActive,
    );
  }
}

enum AttendanceStatus { notPunchedIn, punchedIn, onBreak, punchedOut }

class WorkerAttendance {
  final String workerId;
  final DateTime? punchInTime;
  final DateTime? punchOutTime;
  final String? punchInImagePath;
  final String? punchOutImagePath;
  final AttendanceStatus status;
  final Duration workDuration;
  final Duration breakDuration;
  final DateTime? lastBreakStart;
  final bool isOnBreak;

  WorkerAttendance({
    required this.workerId,
    this.punchInTime,
    this.punchOutTime,
    this.punchInImagePath,
    this.punchOutImagePath,
    this.status = AttendanceStatus.notPunchedIn,
    this.workDuration = Duration.zero,
    this.breakDuration = Duration.zero,
    this.lastBreakStart,
    this.isOnBreak = false,
  });

  WorkerAttendance copyWith({
    String? workerId,
    DateTime? punchInTime,
    DateTime? punchOutTime,
    String? punchInImagePath,
    String? punchOutImagePath,
    AttendanceStatus? status,
    Duration? workDuration,
    Duration? breakDuration,
    DateTime? lastBreakStart,
    bool? isOnBreak,
  }) {
    return WorkerAttendance(
      workerId: workerId ?? this.workerId,
      punchInTime: punchInTime ?? this.punchInTime,
      punchOutTime: punchOutTime ?? this.punchOutTime,
      punchInImagePath: punchInImagePath ?? this.punchInImagePath,
      punchOutImagePath: punchOutImagePath ?? this.punchOutImagePath,
      status: status ?? this.status,
      workDuration: workDuration ?? this.workDuration,
      breakDuration: breakDuration ?? this.breakDuration,
      lastBreakStart: lastBreakStart ?? this.lastBreakStart,
      isOnBreak: isOnBreak ?? this.isOnBreak,
    );
  }

  String get formattedWorkDuration {
    final hours = workDuration.inHours;
    final minutes = workDuration.inMinutes.remainder(60);
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  bool get isPunchedIn =>
      status == AttendanceStatus.punchedIn ||
      status == AttendanceStatus.onBreak;
}