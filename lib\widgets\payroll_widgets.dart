import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PayrollChart extends StatelessWidget {
  final double grossPay;
  final double earnings;
  final double taxAndDeductions;
  final Color themeColor;

  const PayrollChart({
    super.key,
    required this.grossPay,
    required this.earnings,
    required this.taxAndDeductions,
    required this.themeColor,
  });

  @override
  Widget build(BuildContext context) {
    final earningsPercentage = earnings / grossPay;

    return Row(
      children: [
        // Circular Chart
        SizedBox(
          width: 120,
          height: 120,
          child: Stack(
            children: [
              // Background circle
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.grey[200],
                ),
              ),
              // Earnings segment
              SizedBox(
                width: 120,
                height: 120,
                child: CustomPaint(
                  painter: <PERSON><PERSON>hartPainter(
                    earningsPercentage,
                    themeColor,
                    Colors.grey[200]!,
                  ),
                ),
              ),
              // Center content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '\$${grossPay.toStringAsFixed(0)}',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: themeColor,
                      ),
                    ),
                    Text(
                      'Gross Pay',
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: themeColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 20),
        // Legend
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem(
                'Earning \$${earnings.toStringAsFixed(2)}',
                themeColor,
              ),
              const SizedBox(height: 8),
              _buildLegendItem(
                'Tax & Deduction \$${taxAndDeductions.toStringAsFixed(2)}',
                Colors.grey[400]!,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String text, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }
}

class CircleChartPainter extends CustomPainter {
  final double percentage;
  final Color earningsColor;
  final Color backgroundColor;

  CircleChartPainter(this.percentage, this.earningsColor, this.backgroundColor);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Earnings arc
    final earningsPaint = Paint()
      ..color = earningsColor
      ..style = PaintingStyle.fill;

    final rect = Rect.fromCircle(center: center, radius: radius);
    final startAngle = -90 * (3.14159 / 180); // Start from top
    final sweepAngle =
        percentage * 2 * 3.14159; // Convert percentage to radians

    canvas.drawArc(rect, startAngle, sweepAngle, true, earningsPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class PayrollCard extends StatelessWidget {
  final String title;
  final String value;
  final Color? valueColor;
  final VoidCallback? onTap;

  const PayrollCard({
    super.key,
    required this.title,
    required this.value,
    this.valueColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: valueColor ?? Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
