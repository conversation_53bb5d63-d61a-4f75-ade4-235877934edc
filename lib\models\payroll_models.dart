class PayrollMonth {
  final String month;
  final String dateRange;
  final double amount;
  final bool isSelected;

  PayrollMonth({
    required this.month,
    required this.dateRange,
    required this.amount,
    this.isSelected = false,
  });
}

class PayslipSummary {
  final String netPay;
  final String period;
  final double grossPay;
  final double earnings;
  final double taxAndDeductions;
  final int totalHours;
  final String nextPaycheck;

  PayslipSummary({
    required this.netPay,
    required this.period,
    required this.grossPay,
    required this.earnings,
    required this.taxAndDeductions,
    required this.totalHours,
    required this.nextPaycheck,
  });
}

class EarningDetail {
  final String component;
  final double amount;

  EarningDetail({required this.component, required this.amount});
}

class EarningDetails {
  final List<EarningDetail> details;
  final double totalEarning;

  EarningDetails({required this.details, required this.totalEarning});
}


