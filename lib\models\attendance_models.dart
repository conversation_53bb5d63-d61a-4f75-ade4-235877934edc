class MonthlyAttendance {
  final String month;
  final int daysPresent;
  final int totalWorkingDays;
  final int officialLeaves;

  MonthlyAttendance({
    required this.month,
    required this.daysPresent,
    required this.totalWorkingDays,
    required this.officialLeaves,
  });
}

class DailyAttendance {
  final DateTime date;
  final String dayOfWeek;
  final String checkIn;
  final String checkOut;
  final bool isWorkingDay;

  DailyAttendance({
    required this.date,
    required this.dayOfWeek,
    required this.checkIn,
    required this.checkOut,
    required this.isWorkingDay,
  });
}