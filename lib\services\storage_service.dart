import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _keyAuthToken = 'auth_token';
  static const String _keyCompanyName = 'company_name';
  static const String _keyCompanyUrl = 'company_url';
  static const String _keyUserEmail = 'user_email';
  static const String _keyIsFirstTime = 'is_first_time';

  // Save authentication data
  static Future<void> saveAuthData({
    required String token,
    required String companyName,
    required String companyUrl,
    required String userEmail,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyAuthToken, token);
    await prefs.setString(_keyCompanyName, companyName);
    await prefs.setString(_keyCompanyUrl, companyUrl);
    await prefs.setString(_keyUserEmail, userEmail);
    await prefs.setBool(_keyIsFirstTime, false);
  }

  // Get authentication token
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyAuthToken);
  }

  // Get company name
  static Future<String?> getCompanyName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyCompanyName);
  }

  // Get company URL
  static Future<String?> getCompanyUrl() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyCompanyUrl);
  }

  // Get user email
  static Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUserEmail);
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getAuthToken();
    final companyUrl = await getCompanyUrl();
    return token != null && token.isNotEmpty && companyUrl != null && companyUrl.isNotEmpty;
  }

  // Check if it's first time opening the app
  static Future<bool> isFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyIsFirstTime) ?? true;
  }

  // Clear all stored data (logout)
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyAuthToken);
    await prefs.remove(_keyCompanyName);
    await prefs.remove(_keyCompanyUrl);
    await prefs.remove(_keyUserEmail);
    // Don't remove _keyIsFirstTime on logout, only on app uninstall
  }

  // Get stored auth data as a map
  static Future<Map<String, String?>> getStoredAuthData() async {
    return {
      'token': await getAuthToken(),
      'companyName': await getCompanyName(),
      'companyUrl': await getCompanyUrl(),
      'userEmail': await getUserEmail(),
    };
  }
}
