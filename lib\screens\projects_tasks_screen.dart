import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lucent_employee_app/models/project_models.dart';

class ProjectTasksScreen extends StatelessWidget {
  final Project project;

  const ProjectTasksScreen({super.key, required this.project});

  @override
  Widget build(BuildContext context) {
    final themeColor = const Color(0xFF561c24);

    // Mock tasks (replace later with API)
    final tasks = [
      Task(
        id: '1',
        title: 'Foundation Work',
        description: 'Complete foundation work for building',
        date: DateTime(2023, 4, 22),
        location: 'Site A',
        status: 'Pending',
      ),
      Task(
        id: '2',
        title: 'Brick Work',
        description: 'Start brickwork on ground floor',
        date: DateTime(2023, 4, 25),
        location: 'Site B',
        status: 'Completed',
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Tasks - ${project.name}",
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: themeColor,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: tasks.length,
        itemBuilder: (context, index) {
          return _buildTaskCard(tasks[index], themeColor);
        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: themeColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        onPressed: () {
          // TODO: Navigate to Add Task Screen
        },
        child: const Icon(Icons.add, size: 28, color: Colors.white),
      ),
      backgroundColor: const Color(0xFFF7F6F9),
    );
  }

  Widget _buildTaskCard(Task task, Color themeColor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.07),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title + Status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    task.title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: themeColor,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: task.status == "Completed"
                        ? Colors.green.withOpacity(0.15)
                        : Colors.orange.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Text(
                    task.status,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: task.status == "Completed"
                          ? Colors.green
                          : Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Description
            Text(
              task.description,
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 14),

            // Date & Location
            Row(
              children: [
                Icon(Icons.calendar_today, size: 18, color: themeColor),
                const SizedBox(width: 6),
                Text(
                  DateFormat('dd MMM yyyy').format(task.date),
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: Colors.grey[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Icon(Icons.location_on, size: 18, color: themeColor),
                const SizedBox(width: 6),
                Text(
                  task.location,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: Colors.grey[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
