import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/providers/auth_provider.dart';
import 'package:lucent_employee_app/screens/attendance_screen.dart';
import 'package:lucent_employee_app/screens/company_select_screen.dart';
import 'package:lucent_employee_app/screens/leave_screen.dart';
import 'package:lucent_employee_app/screens/payroll_screen.dart';
import 'package:lucent_employee_app/screens/projects_screen.dart';
import 'package:lucent_employee_app/screens/worker_attendance_screen.dart';
import 'package:lucent_employee_app/services/storage_service.dart';

import 'package:lucent_employee_app/screens/mc_submission_screen.dart';

class Sidebar extends ConsumerWidget {
  const Sidebar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeColor = const Color(0xFF561c24);

    return Drawer(
      child: Column(
        children: [
          Container(
            height: 250,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [themeColor, themeColor.withValues(alpha: 0.8)],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    CircleAvatar(
                      radius: 35,
                      backgroundColor: Colors.white,
                      child: Icon(Icons.person, size: 40, color: themeColor),
                    ),
                    const SizedBox(height: 16),
                    FutureBuilder<Map<String, String?>>(
                      future: StorageService.getStoredAuthData(),
                      builder: (context, snapshot) {
                        final userData = snapshot.data;
                        final userName =
                            userData?['userEmail']
                                ?.split('@')[0]
                                .toUpperCase() ??
                            'USER';
                        final userEmail =
                            userData?['userEmail'] ?? '<EMAIL>';
                        final companyName =
                            userData?['companyName'] ?? 'Company';

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              userName,
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              userEmail,
                              style: GoogleFonts.poppins(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              companyName,
                              style: GoogleFonts.poppins(
                                color: Colors.white60,
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(Icons.access_time, 'Attendance', () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AttendanceScreen(),
                    ),
                  );
                }),
                _buildDrawerItem(Icons.beach_access, 'Leave', () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LeaveScreen(),
                    ),
                  );
                }),
                _buildDrawerItem(Icons.account_balance_wallet, 'Payslip', () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PayrollScreen(),
                    ),
                  );
                }),
                _buildDrawerItem(Icons.assignment, 'MC Submission', () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const McSubmissionScreen(),
                    ),
                  );
                }),
                _buildDrawerItem(Icons.access_time, 'Worker Attendance', () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WorkerAttendanceScreen(),
                    ),
                  );
                }),
                _buildDrawerItem(Icons.edit_document, 'Common Documents', () {}),
                _buildDrawerItem(Icons.task_outlined, 'Projects', () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ProjectsScreen(),
                    ),
                  );
                }),
                const Divider(),
                _buildDrawerItem(Icons.person, 'My Profile', () {}),
                _buildDrawerItem(Icons.settings, 'Settings', () {}),
                _buildDrawerItem(
                  Icons.logout,
                  'Logout',
                  () => _handleLogout(context, ref),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context, WidgetRef ref) async {
    // Show confirmation dialog
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Logout',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: GoogleFonts.poppins(fontSize: 14),
          ),
          actionsPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(dialogContext).pop(false),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: const BorderSide(color: Colors.grey),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: GoogleFonts.poppins(color: Colors.grey[700]),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(dialogContext).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF561c24),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: Text(
                        'Logout',
                        style: GoogleFonts.poppins(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      try {
        // Get current company URL to clear auth state
        final storedData = await StorageService.getStoredAuthData();
        final companyUrl = storedData['companyUrl'];

        if (companyUrl != null) {
          // Clear auth state
          await ref.read(authNotifierProvider(companyUrl).notifier).logout();
        } else {
          // Fallback: clear storage directly
          await StorageService.clearAuthData();
        }

        // Navigate to company select screen
        if (context.mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) => const CompanySelectScreen()),
            (route) => false,
          );
        }
      } catch (e) {
        // Show error message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error during logout: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Widget _buildDrawerItem(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF561c24)),
      title: Text(
        title,
        style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
      ),
      onTap: onTap,
    );
  }
}
