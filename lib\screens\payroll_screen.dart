import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucent_employee_app/models/payroll_models.dart';
import 'package:lucent_employee_app/screens/payslip_summary_screen.dart';

class PayrollScreen extends StatefulWidget {
  const PayrollScreen({super.key});

  @override
  State<PayrollScreen> createState() => _PayrollScreenState();
}

class _PayrollScreenState extends State<PayrollScreen> {
  final themeColor = const Color(0xFF561c24);

  // Sample payroll data
  final List<PayrollMonth> payrollMonths = [
    PayrollMonth(
      month: 'Jan',
      dateRange: '01/01/2023 - 31/01/2023',
      amount: 3680.35,
      isSelected: true,
    ),
    PayrollMonth(
      month: 'Dec',
      dateRange: '01/12/2022 - 31/12/2022',
      amount: 3900.00,
    ),
    PayrollMonth(
      month: 'Nov',
      dateRange: '01/11/2022 - 30/11/2022',
      amount: 3750.50,
    ),
    PayrollMonth(
      month: 'Oct',
      dateRange: '01/10/2022 - 31/10/2022',
      amount: 3900.00,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: themeColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'PAYSLIP',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // User greeting section
        Container(
          width: double.infinity,
          color: themeColor,
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'HEY JOHN HENDRY,',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Welcome To Pay Summary Details',
                style: GoogleFonts.poppins(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
        // White content area
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  Expanded(child: _buildPayrollList()),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  
  Widget _buildPayrollList() {
    return ListView.builder(
      itemCount: payrollMonths.length,
      itemBuilder: (context, index) {
        final month = payrollMonths[index];
        return _buildPayrollItem(month);
      },
    );
  }

  Widget _buildPayrollItem(PayrollMonth month) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const PayslipSummaryScreen()),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: month.isSelected ? themeColor : Colors.grey[200]!,
            width: month.isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: month.isSelected ? themeColor : Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    month.month,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    month.dateRange,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Text(
              '${month.amount.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: month.isSelected
                    ? FontWeight.bold
                    : FontWeight.w600,
                color: month.isSelected ? themeColor : Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
