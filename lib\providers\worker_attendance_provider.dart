import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import 'package:lucent_employee_app/models/worker_attendance_models.dart';

// Sample workers data
final sampleWorkersProvider = Provider<List<Worker>>((ref) {
  return [
    Worker(id: '1', name: '<PERSON>', designation: 'Construction Worker'),
    Worker(id: '2', name: '<PERSON><PERSON><PERSON>', designation: 'Supervisor'),
    Worker(id: '3', name: '<PERSON><PERSON>', designation: '<PERSON>'),
    Worker(id: '4', name: '<PERSON><PERSON>', designation: 'Site Engineer'),
    Worker(id: '5', name: '<PERSON><PERSON>', designation: 'Electrician'),
  ];
});

// Worker attendance state notifier
class WorkerAttendanceNotifier
    extends StateNotifier<Map<String, WorkerAttendance>> {
  Timer? _timer;

  WorkerAttendanceNotifier() : super({}) {
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateWorkDurations();
    });
  }

  void _updateWorkDurations() {
    final now = DateTime.now();
    final updatedState = <String, WorkerAttendance>{};
    bool hasUpdates = false;

    for (final entry in state.entries) {
      final attendance = entry.value;
      if (attendance.isPunchedIn && attendance.punchInTime != null) {
        // Calculate total work duration excluding break time
        final totalElapsed = now.difference(attendance.punchInTime!);
        Duration currentBreakTime = attendance.breakDuration;

        // Add current break time if on break
        if (attendance.isOnBreak && attendance.lastBreakStart != null) {
          currentBreakTime += now.difference(attendance.lastBreakStart!);
        }

        final actualWorkDuration = totalElapsed - currentBreakTime;

        updatedState[entry.key] = attendance.copyWith(
          workDuration: actualWorkDuration,
        );
        hasUpdates = true;
      } else {
        updatedState[entry.key] = attendance;
      }
    }

    if (hasUpdates) {
      state = updatedState;
    }
  }

  void punchIn(String workerId, String imagePath) {
    final now = DateTime.now();
    state = {
      ...state,
      workerId: WorkerAttendance(
        workerId: workerId,
        punchInTime: now,
        punchInImagePath: imagePath,
        status: AttendanceStatus.punchedIn,
      ),
    };
  }

  void punchOut(String workerId, String imagePath) {
    final currentAttendance = state[workerId];
    if (currentAttendance != null) {
      final now = DateTime.now();
      state = {
        ...state,
        workerId: currentAttendance.copyWith(
          punchOutTime: now,
          punchOutImagePath: imagePath,
          status: AttendanceStatus.punchedOut,
        ),
      };
    }
  }

  void toggleBreak(String workerId) {
    final currentAttendance = state[workerId];
    if (currentAttendance != null && currentAttendance.isPunchedIn) {
      final now = DateTime.now();

      if (currentAttendance.isOnBreak) {
        // End break
        Duration additionalBreakTime = Duration.zero;
        if (currentAttendance.lastBreakStart != null) {
          additionalBreakTime = now.difference(
            currentAttendance.lastBreakStart!,
          );
        }

        state = {
          ...state,
          workerId: currentAttendance.copyWith(
            isOnBreak: false,
            status: AttendanceStatus.punchedIn,
            breakDuration:
                currentAttendance.breakDuration + additionalBreakTime,
            lastBreakStart: null,
          ),
        };
      } else {
        // Start break
        state = {
          ...state,
          workerId: currentAttendance.copyWith(
            isOnBreak: true,
            status: AttendanceStatus.onBreak,
            lastBreakStart: now,
          ),
        };
      }
    }
  }

  WorkerAttendance? getAttendance(String workerId) {
    return state[workerId];
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

// Provider for worker attendance
final workerAttendanceProvider =
    StateNotifierProvider<
      WorkerAttendanceNotifier,
      Map<String, WorkerAttendance>
    >((ref) {
      return WorkerAttendanceNotifier();
    });

// Provider for getting attendance of a specific worker
final workerAttendanceByIdProvider = Provider.family<WorkerAttendance?, String>(
  (ref, workerId) {
    final attendanceMap = ref.watch(workerAttendanceProvider);
    return attendanceMap[workerId];
  },
);
